{"name": "next-pages-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@nextui-org/react": "^2.1.10", "@types/node": "20.5.7", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "axios": "^1.5.0", "clsx": "^2.0.0", "eslint": "8.48.0", "eslint-config-next": "13.4.19", "framer-motion": "^10.16.4", "intl-messageformat": "^10.5.0", "next": "13.4.19", "next-auth": "^4.23.1", "next-themes": "^0.2.1", "postcss": "8.4.29", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^4.11.0", "tailwind-variants": "^0.1.13", "tailwindcss": "3.3.3", "typescript": "5.0.4"}}