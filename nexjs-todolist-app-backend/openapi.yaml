openapi: 3.0.0
info:
  version: 1.0.0
  title: Todo API
  description: A simple Todo API
paths:
  /users/{userId}/todos:
    get:
      summary: List all todos for a user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: A list of todos
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TodoResponse'
    post:
      summary: Create a new todo for a user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TodoRequest'
      responses:
        '201':
          description: Todo created
  /users/{userId}/todos/{id}:
    get:
      summary: Get a todo by ID for a user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Single todo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TodoResponse'
    put:
      summary: Update a todo by ID for a user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TodoRequest'
      responses:
        '200':
          description: Todo updated
    delete:
      summary: Delete a todo by ID for a user
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '204':
          description: Todo deleted

components:
  schemas:
    TodoResponse:
      type: object
      properties:
        id:
          type: integer
        userId:
          type: string
        title:
          type: string
        description:
          type: string
      required:
        - id
        - userId
        - title

    TodoRequest:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
      required:
        - title
