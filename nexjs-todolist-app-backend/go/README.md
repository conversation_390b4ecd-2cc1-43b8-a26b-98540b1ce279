# Go API Server for swagger

A simple Todo API

## Overview
This server was generated by the [swagger-codegen]
(https://github.com/swagger-api/swagger-codegen) project.  
By using the [OpenAPI-Spec](https://github.com/OAI/OpenAPI-Specification) from a remote server, you can easily generate a server stub.  
-

To see how to make this your own, look here:

[README](https://github.com/swagger-api/swagger-codegen/blob/master/README.md)

- API version: 1.0.0
- Build date: 2023-09-20T02:22:37.876973523Z[GMT]


### Running the server
To run the server, follow these simple steps:

```
go run main.go
```

