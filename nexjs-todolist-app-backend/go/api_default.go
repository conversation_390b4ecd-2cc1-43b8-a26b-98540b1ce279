/*
 * Todo API
 *
 * A simple Todo API
 *
 * API version: 1.0.0
 * Generated by: Swagger Codegen (https://github.com/swagger-api/swagger-codegen.git)
 */
package swagger

import (
	"encoding/json"
	"net/http"
	"strconv"

	"github.com/gorilla/mux"
	"golang.org/x/exp/slices"
)

var lastId = 0

func getUserId(r *http.Request) string {
	return mux.Vars(r)["userId"]
}

func getTodoId(r *http.Request) (int32, error) {
	id := mux.Vars(r)["id"]
	v, err := strconv.Atoi(id)
	if err != nil {
		return 0, err
	}
	return int32(v), nil
}

func writeJSONResponse(w http.ResponseWriter, data interface{}) {
	response, _ := json.Marshal(data)
	w.Header().Set("Content-Type", "application/json; charset=UTF-8")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Origin", "*")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "*")
	w.WriteHeader(http.StatusOK)
	_, _ = w.Write(response)
}

func makeSampleTodos(userId string) []TodoResponse {
	items := []TodoResponse{}
	items = append(items, TodoResponse{
		Id:          1,
		UserId:      userId,
		Title:       "Buy milk",
		Description: "Buy 2 gallons of milk from the market",
	})
	items = append(items, TodoResponse{
		Id:          2,
		UserId:      userId,
		Title:       "Buy eggs",
		Description: "Buy 2 dozens of eggs from the market",
	})
	items = append(items, TodoResponse{
		Id:          3,
		UserId:      userId,
		Title:       "Buy bread",
		Description: "Buy 2 loaves of bread from the market",
	})
	return items
}

var todos = []TodoResponse{}

func UsersUserIdTodosGet(w http.ResponseWriter, r *http.Request) {
	userId := getUserId(r)
	userTodos := []TodoResponse{}
	for _, todo := range todos {
		if todo.UserId == userId {
			userTodos = append(userTodos, todo)
		}
	}
	if len(userTodos) == 0 {
		userTodos = makeSampleTodos(userId)
		todos = append(todos, userTodos...)
	}
	writeJSONResponse(w, userTodos)
}

func UsersUserIdTodosIdDelete(w http.ResponseWriter, r *http.Request) {
	userId := getUserId(r)
	todoId, err := getTodoId(r)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	idx := slices.IndexFunc(todos, func(todo TodoResponse) bool {
		return todo.Id == todoId && todo.UserId == userId
	})
	if idx == -1 {
		w.WriteHeader(http.StatusNotFound)
		return
	}
	todos = append(todos[:idx], todos[idx+1:]...)
	w.Header().Set("Content-Type", "application/json; charset=UTF-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "*")
	w.WriteHeader(http.StatusOK)
}

func UsersUserIdTodosIdGet(w http.ResponseWriter, r *http.Request) {
	userId := getUserId(r)
	todoId, err := getTodoId(r)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	idx := slices.IndexFunc(todos, func(todo TodoResponse) bool {
		return todo.Id == todoId && todo.UserId == userId
	})
	if idx == -1 {
		w.WriteHeader(http.StatusNotFound)
		return
	}
	writeJSONResponse(w, todos[idx])
}

func UsersUserIdTodosIdPut(w http.ResponseWriter, r *http.Request) {
	userId := getUserId(r)
	todoId, err := getTodoId(r)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	idx := slices.IndexFunc(todos, func(todo TodoResponse) bool {
		return todo.Id == todoId && todo.UserId == userId
	})
	if idx == -1 {
		w.WriteHeader(http.StatusNotFound)
		return
	}
	var update TodoResponse
	if err := json.NewDecoder(r.Body).Decode(&update); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	todos[idx].Title = update.Title
	todos[idx].Description = update.Description
	writeJSONResponse(w, todos[idx])
}

func UsersUserIdTodosPost(w http.ResponseWriter, r *http.Request) {
	userId := getUserId(r)
	var todo TodoResponse
	if err := json.NewDecoder(r.Body).Decode(&todo); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	todo.UserId = userId
	todo.Id = int32(3 + lastId + 1)
	lastId++
	todos = append(todos, todo)
	writeJSONResponse(w, todo)
}

func respondOptions(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json; charset=UTF-8")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "*")
	w.WriteHeader(http.StatusOK)
}
