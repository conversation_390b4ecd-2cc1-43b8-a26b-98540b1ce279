# Copyright (c) 2024, WSO2 LLC. (https://www.wso2.com/) All Rights Reserved.
#
# WSO2 LLC. licenses this file to you under the Apache License,
# Version 2.0 (the "License"); you may not use this file except
# in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied. See the License for the
# specific language governing permissions and limitations
# under the License.

# +required The configuration file schema version
schemaVersion: 1.2

# +optional Incoming connection details for the component
endpoints:
  # +required Unique name for the endpoint.
  # This name will be used when generating the managed API
  - name: todo_list
    # +optional Display name for the endpoint.
    displayName: Todo List
    # +required Service section has the user service endpoint details
    service:
      # +optional Base path of the API that gets exposed via the endpoint.
      # This is mandatory if the endpoint type is set to REST or GraphQL.
      basePath: /
      # +required Numeric port value that gets exposed via the endpoint
      port: 8080
    # +required Type of traffic that the endpoint is accepting.
    # Allowed values: REST, GraphQL, GRPC, TCP, UDP.
    type: REST
    # +optional Network level visibilities of the endpoint.
    # Accepted values: Project|Organization|Public(Default).
    networkVisibilities:
      - Project
    # +optional The path to the schema definition file.
    # Defaults to wildcard route if not specified.
    # This is only applicable to REST endpoint types.
    # The path should be relative to the docker context.
    schemaFilePath: openapi.yaml
